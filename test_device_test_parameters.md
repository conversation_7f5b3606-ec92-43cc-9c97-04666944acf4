# 设备测试参数发送功能测试

## 功能概述

在 `DeviceTestService.StartDeviceTestAsync` 方法中实现了向设备发送测试参数的功能。该功能在测试启动流程中的第6步执行，在启动测试之后、更新设备状态之前。

## 实现的参数发送

### 1. 定时运行小时 (`定时运行_小时`)
- **参数来源**: `request.ExtraConfig["hours"]`
- **协议项名称**: `定时运行_小时`
- **数据类型**: Int32
- **地址**: 3050

### 2. 定时运行分钟 (`定时运行_分钟`)
- **参数来源**: `request.ExtraConfig["minutes"]`
- **协议项名称**: `定时运行_分钟`
- **数据类型**: Int32
- **地址**: 3051

### 3. 定时已运行小时 (`定时已运行_小时`)
- **参数来源**: `request.ExtraConfig["elapsedHours"]` 或默认值 0
- **协议项名称**: `定时已运行_小时`
- **数据类型**: Int32
- **地址**: 3042

### 4. 定时已运行分钟 (`定时已运行_分钟`)
- **参数来源**: `request.ExtraConfig["elapsedMinutes"]` 或默认值 0
- **协议项名称**: `定时已运行_分钟`
- **数据类型**: Int32
- **地址**: 3041

### 5. 当前程式步骤 (`当前程式步骤`)
- **参数来源**: `request.ExtraConfig["currentProgramStep"]` 或程式模式默认值 1
- **协议项名称**: `当前程式步骤`
- **数据类型**: Int32
- **地址**: 3056

### 6. 当前启动模式 (`当前启动模式`) - 最后发送
- **参数来源**: `request.ExtraConfig["startupMode"]` 或默认值 "ColdStart"
- **协议项名称**: `当前启动模式`
- **数据类型**: Int32
- **地址**: 3012
- **值映射**:
  - `"ColdStart"` → 1 (冷启动)
  - `"HotStart"` → 2 (热启动)
  - `"Reset"` → 0 (复位启动)

## 测试用例

### 定时模式测试
```json
{
  "deviceId": 1,
  "testName": "定时测试",
  "executionType": "timed",
  "extraConfig": {
    "startupMode": "ColdStart",
    "temperature": 25.5,
    "humidity": 60.0,
    "hours": 2,
    "minutes": 30,
    "elapsedHours": 0,
    "elapsedMinutes": 0
  }
}
```

### 程式模式测试
```json
{
  "deviceId": 1,
  "testName": "程式测试",
  "executionType": "program",
  "extraConfig": {
    "startupMode": "HotStart",
    "currentProgramStep": 1,
    "selectedProgramId": 123
  }
}
```

## 错误处理

- 如果 `ExtraConfig` 为 null，记录警告并跳过参数发送
- 如果参数转换失败，记录错误但不中断测试启动流程
- 如果协议项发送失败，记录错误但继续发送其他参数
- 所有异常都被捕获并记录，不会影响测试的正常启动

## 日志记录

每个参数的发送都会记录详细的日志信息，包括：
- 参数名称和值
- 发送结果（成功/失败）
- 设备ID
- 错误信息（如有）

## 执行顺序

1. 定时运行小时
2. 定时运行分钟
3. 定时已运行小时
4. 定时已运行分钟
5. 当前程式步骤
6. **当前启动模式（最后发送）**

这个顺序确保了启动模式作为最后一个参数发送，符合设备的启动要求。
