using EnvizonController.Application.Services;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Services;
using EnvizonController.Shared.DTOs;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Xunit;

namespace EnvizonController.Server.Tests.UnitTests
{
    /// <summary>
    /// 测试执行策略单元测试
    /// </summary>
    public class TestExecutionStrategiesTests
    {
        private readonly Mock<IServiceProvider> _mockServiceProvider;
        private readonly Mock<ILogger> _mockLogger;
        private readonly Mock<ITestRunDomainService> _mockTestRunService;
        private readonly Mock<IServiceScope> _mockScope;

        public TestExecutionStrategiesTests()
        {
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockLogger = new Mock<ILogger>();
            _mockTestRunService = new Mock<ITestRunDomainService>();
            _mockScope = new Mock<IServiceScope>();

            // 设置服务提供者模拟
            _mockScope.Setup(s => s.ServiceProvider.GetRequiredService<ITestRunDomainService>())
                     .Returns(_mockTestRunService.Object);
            _mockServiceProvider.Setup(sp => sp.CreateScope())
                               .Returns(_mockScope.Object);
        }

        [Fact]
        public void TestExecutionStrategyFactory_CreateStrategy_ReturnsCorrectStrategy()
        {
            // Arrange
            var factory = new TestExecutionStrategyFactory(_mockServiceProvider.Object, 
                Mock.Of<ILogger<TestExecutionStrategyFactory>>());

            // Act & Assert
            Assert.IsType<ManualTestExecutionStrategy>(factory.CreateStrategy("manual"));
            Assert.IsType<ProgramTestExecutionStrategy>(factory.CreateStrategy("program"));
            Assert.IsType<ProgramLinkTestExecutionStrategy>(factory.CreateStrategy("programlink"));
            Assert.IsType<ConstantValueTestExecutionStrategy>(factory.CreateStrategy("constantvalue"));
            Assert.IsType<TimedTestExecutionStrategy>(factory.CreateStrategy("timed"));
            Assert.IsType<CustomTestExecutionStrategy>(factory.CreateStrategy("custom"));
            Assert.IsType<ManualTestExecutionStrategy>(factory.CreateStrategy("unknown")); // 默认策略
        }

        [Fact]
        public async Task ProgramLinkTestExecutionStrategy_CreateTestRunAsync_WithExtraConfig_Success()
        {
            // Arrange
            var strategy = new ProgramLinkTestExecutionStrategy(_mockServiceProvider.Object, _mockLogger.Object);
            var device = new Device { Id = 1, Name = "Test Device" };
            var request = new DeviceTestStartRequest
            {
                DeviceId = 1,
                TestName = "程式链接测试",
                Description = "测试程式链接",
                ExecutionId = 123,
                ExtraConfig = new Dictionary<string, object>
                {
                    ["startupMode"] = "HotStart",
                    ["timingRange"] = "开始计时",
                    ["customParam"] = "自定义值"
                }
            };

            var expectedTestRun = TestRun.Create("程式链接测试", 1, "Created", "测试程式链接");
            _mockTestRunService.Setup(s => s.CreateTestFromProgramLinkAsync(
                It.IsAny<long>(), It.IsAny<string>(), It.IsAny<long>(), 
                It.IsAny<string>(), It.IsAny<Dictionary<string, object>>()))
                .ReturnsAsync(expectedTestRun);

            // Act
            var result = await strategy.CreateTestRunAsync(request, device);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("程式链接测试", result.Name);
            _mockTestRunService.Verify(s => s.CreateTestFromProgramLinkAsync(
                123, "程式链接测试", 1, "测试程式链接", request.ExtraConfig), Times.Once);
        }

        [Fact]
        public async Task ConstantValueTestExecutionStrategy_CreateTestRunAsync_ValidParameters_Success()
        {
            // Arrange
            var strategy = new ConstantValueTestExecutionStrategy(_mockServiceProvider.Object, _mockLogger.Object);
            var device = new Device { Id = 1, Name = "Test Device" };
            var request = new DeviceTestStartRequest
            {
                DeviceId = 1,
                TestName = "定值测试",
                Description = "定值模式测试",
                ExtraConfig = new Dictionary<string, object>
                {
                    ["startupMode"] = "ColdStart",
                    ["temperature"] = 25.5,
                    ["humidity"] = 60.0
                }
            };

            // Act
            var result = await strategy.CreateTestRunAsync(request, device);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("定值测试", result.Name);
            Assert.Equal("ConstantValue", result.ExecutionType);
            Assert.Contains("ConstantValue", result.ExecutionConfigJson);
        }

        [Fact]
        public async Task ConstantValueTestExecutionStrategy_CreateTestRunAsync_MissingTemperature_ThrowsException()
        {
            // Arrange
            var strategy = new ConstantValueTestExecutionStrategy(_mockServiceProvider.Object, _mockLogger.Object);
            var device = new Device { Id = 1, Name = "Test Device" };
            var request = new DeviceTestStartRequest
            {
                DeviceId = 1,
                TestName = "定值测试",
                ExtraConfig = new Dictionary<string, object>
                {
                    ["humidity"] = 60.0
                    // 缺少 temperature
                }
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => strategy.CreateTestRunAsync(request, device));
            Assert.Contains("温度设定值", exception.Message);
        }

        [Fact]
        public async Task TimedTestExecutionStrategy_CreateTestRunAsync_ValidParameters_Success()
        {
            // Arrange
            var strategy = new TimedTestExecutionStrategy(_mockServiceProvider.Object, _mockLogger.Object);
            var device = new Device { Id = 1, Name = "Test Device" };
            var request = new DeviceTestStartRequest
            {
                DeviceId = 1,
                TestName = "定时测试",
                Description = "定时模式测试",
                ExtraConfig = new Dictionary<string, object>
                {
                    ["startupMode"] = "ColdStart",
                    ["temperature"] = 25.5,
                    ["humidity"] = 60.0,
                    ["days"] = 1,
                    ["hours"] = 2,
                    ["minutes"] = 30
                }
            };

            // Act
            var result = await strategy.CreateTestRunAsync(request, device);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("定时测试", result.Name);
            Assert.Equal("Timed", result.ExecutionType);
            // 验证预计持续时间：1天 + 2小时 + 30分钟 = 86400 + 7200 + 1800 = 95400秒
            Assert.Equal(95400, result.EstimatedDurationSeconds);
        }

        [Fact]
        public void TestRun_CreateFromProgramLink_WithExtraConfig_StoresParameters()
        {
            // Arrange
            var programLink = new ProgramLink 
            { 
                Id = 1, 
                Name = "测试程式链接", 
                CycleCount = 2,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
            
            var extraConfig = new Dictionary<string, object>
            {
                ["startupMode"] = "HotStart",
                ["timingRange"] = "开始计时",
                ["customParam"] = "自定义值"
            };

            var testRun = TestRun.Create("测试", 1);

            // Act
            testRun.CreateFromProgramLink(programLink, extraConfig);

            // Assert
            Assert.Equal("ProgramLink", testRun.ExecutionType);
            Assert.NotEmpty(testRun.ExecutionConfigJson);

            // 验证快照中包含额外参数
            var snapshot = testRun.GetProgramLinkSnapshot();
            Assert.NotNull(snapshot);
            Assert.Equal("HotStart", snapshot.StartupMode);
            Assert.Equal("开始计时", snapshot.TimingRange);
            Assert.NotNull(snapshot.AdditionalParameters);
            Assert.Equal("自定义值", snapshot.AdditionalParameters["customParam"]);
        }
    }
}
